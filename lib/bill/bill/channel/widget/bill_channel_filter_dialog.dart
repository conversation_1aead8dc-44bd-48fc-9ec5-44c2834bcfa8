import 'package:flutter/material.dart';
import '../../../../common/standard.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

import '../../../../iconfont/icon_font.dart';
import '../../../../stockcheck/widget/filter_box_widget.dart';
import 'package:halo_utils/utils/date_util.dart' as DateUtil;

typedef ConfirmCallBack = void Function();

class BillChannelFilterDialog extends Dialog {
  final FilterData filterData;
  final FocusNode _focusNode = FocusNode();
  final EdgeInsets _padding = EdgeInsets.symmetric(horizontal: 22.w);

  BillChannelFilterDialog({Key? key, required this.filterData})
      : super(key: key);

  static double get width => 800.w;

  static double get height => 700.w;

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: ClipRRect(
          borderRadius: BorderRadius.all(Radius.circular(10.w)),
          child: <PERSON>o<PERSON>ontaine<PERSON>(
            color: Colors.white,
            direction: Axis.vertical,
            width: width,
            children: [
              _buildTitle(context),
              buildContent(context),
              SizedBox(
                height: 25.w,
              ),
              _buildBottomButtons(context)
            ],
          ),
        ),
      ),
    );
  }

  ///标题行
  Widget _buildTitle(BuildContext context) {
    return Container(
      color: Colors.transparent,
      height: 80.h,
      padding: _padding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "条件筛选",
            style: TextStyle(
              color: const Color(0xFF333333),
              fontSize: 28.sp,
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => Navigator.pop(context),
            child: Padding(
              padding: EdgeInsets.all(10.w),
              child: IconFont(IconNames.close, size: 20.w),
            ),
          ),
        ],
      ),
    );
  }

  ///content
  Widget buildContent(BuildContext context) {
    return Column(
      children: [
        FilterBox(
          filterTitle: "时间",
          filterBoxType: FilterBoxType.date,
          textStartTimeController: filterData.textStartTimeController,
          textEndTimeController: filterData.textEndTimeController,
          beforeTime: filterData.beforeTime,
          afterTime: filterData.afterTime,
        ),
        FilterBox(
          filterTitle: "单据编号",
          filterBoxType: FilterBoxType.text,
          textEditingController: filterData.textBillNumberController,
        ),
        FilterBox(
          filterTitle: "平台类型",
          filterBoxType: FilterBoxType.multi,
          textEditingController: filterData.textPlatformTypeController,
          dropItems: filterData.platformBoxItems,
          selectDropItems: filterData.selectPlatformBoxItems,
        ),
        FilterBox(
          filterTitle: "网店名称",
          filterBoxType: FilterBoxType.multi,
          textEditingController: filterData.textEshopTypeController,
          dropItems: filterData.eshopBoxItems,
          selectDropItems: filterData.selectEshopBoxItems,
        ),
        FilterBox(
          filterTitle: "交货方式",
          filterBoxType: FilterBoxType.drop,
          textEditingController: filterData.textDeliveryTypeController,
          value: filterData.textDeliveryTypeController.text.isEmpty
              ? null
              : filterData.textDeliveryTypeController.text,
          dropItems: filterData.deliveryBoxItems,
        ),
      ],
    );
  }

  ///底部
  Widget _buildBottomButtons(BuildContext context) {
    return Container(
      color: Colors.white,
      height: 50.h,
      margin: EdgeInsets.only(bottom: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildButton("确定",
              textColor: Colors.white,
              background: const Color(0xFF4679FC), onTap: () {
            clickBtn(context);
          }),
          SizedBox(width: 30.w),
          _buildButton("取消", onTap: () => Navigator.pop(context)),
        ],
      ),
    );
  }

  ///底部按钮
  Widget _buildButton(
    String content, {
    Color textColor = const Color(0xFF333333),
    Color background = Colors.white,
    Color borderColor = const Color(0xFF999999),
    VoidCallback? onTap,
  }) {
    return GestureDetector(
        onTap: onTap,
        child: Container(
          height: 50.h,
          width: 196.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: background,
            borderRadius: BorderRadius.circular(4.w),
            border: borderColor.let(
                (borderColor) => Border.all(color: borderColor, width: 2.w)),
          ),
          child: Text(content,
              style: TextStyle(color: textColor, fontSize: 26.sp)),
        ));
  }

  clickBtn(BuildContext context) {
    FocusScope.of(context).requestFocus(_focusNode);
    Navigator.pop(context, true);
  }
}

class FilterData {
  DateTime beforeTime = DateTime.now().add(const Duration(days: -30));
  DateTime afterTime = DateTime.now();

  ///开始结束
  TextEditingController textStartTimeController = TextEditingController();
  TextEditingController textEndTimeController = TextEditingController();

  ///单据编号
  TextEditingController textBillNumberController = TextEditingController();

  ///平台类型
  TextEditingController textPlatformTypeController = TextEditingController();

  ///平台下拉类型
  List<FilterBoxItem> platformBoxItems = [];

  ///网店名称
  TextEditingController textEshopTypeController = TextEditingController();

  ///网店名称下拉数据
  List<FilterBoxItem> eshopBoxItems = [];

  ///交货方式
  TextEditingController textDeliveryTypeController = TextEditingController();

  ///交货方式下拉数据
  List<FilterBoxItem> deliveryBoxItems = [];

  List<FilterBoxItem> selectPlatformBoxItems = [];

  List<FilterBoxItem> selectEshopBoxItems = [];

  // List<FilterBoxItem> selectDeliveryBoxItems = [];

  FilterData();

  FilterData._(
    this.beforeTime,
    this.afterTime,
    this.textStartTimeController,
    this.textEndTimeController,
    this.textBillNumberController,
    this.textPlatformTypeController,
    this.platformBoxItems,
    this.textEshopTypeController,
    this.eshopBoxItems,
    this.textDeliveryTypeController,
    this.deliveryBoxItems,
    this.selectPlatformBoxItems,
    this.selectEshopBoxItems,
  );

  factory FilterData.build({
    required DateTime beforeTime,
    required DateTime afterTime,
    required List<FilterBoxItem> platformBoxItems,
    required List<FilterBoxItem> eshopBoxItems,
    required List<FilterBoxItem> deliveryBoxItems,
    required List<FilterBoxItem> selectPlatformBoxItems,
    required List<FilterBoxItem> selectEshopBoxItems,
  }) {
    return FilterData._(
        beforeTime,
        afterTime,
        TextEditingController(
            text: DateUtil.DateUtil.getDateStrByDateTime(beforeTime)),
        TextEditingController(
            text: DateUtil.DateUtil.getDateStrByDateTime(afterTime)),
        TextEditingController(),
        TextEditingController(),
        platformBoxItems,
        TextEditingController(),
        eshopBoxItems,
        TextEditingController(),
        deliveryBoxItems,
        selectPlatformBoxItems,
        selectEshopBoxItems);
  }

  FilterData.fromJson(Map<String, dynamic> map) {
    beforeTime = map["beforeTime"];
    afterTime = map["afterTime"];
    textStartTimeController = map["textStartTimeController"];
    textEndTimeController = map["textEndTimeController"];
    textBillNumberController = map["textBillNumberController"];
    textPlatformTypeController = map["textPlatformTypeController"];
    textEshopTypeController = map["textEshopTypeController"];
    textDeliveryTypeController = map["textDeliveryTypeController"];

    if (map["platformBoxItems"] != null) {
      platformBoxItems = [];
      for (var element in (map["platformBoxItems"] as List)) {
        platformBoxItems.add(FilterBoxItem.fromJson(element));
      }
    }

    if (map["eshopBoxItems"] != null) {
      eshopBoxItems = [];
      for (var element in (map["eshopBoxItems"] as List)) {
        eshopBoxItems.add(FilterBoxItem.fromJson(element));
      }
    }

    if (map["deliveryBoxItems"] != null) {
      deliveryBoxItems = [];
      for (var element in (map["deliveryBoxItems"] as List)) {
        deliveryBoxItems.add(FilterBoxItem.fromJson(element));
      }
    }

    if (map["selectPlatformBoxItems"] != null) {
      selectPlatformBoxItems = [];
      for (var element in (map["selectPlatformBoxItems"] as List)) {
        selectPlatformBoxItems.add(FilterBoxItem.fromJson(element));
      }
    }

    if (map["selectEshopBoxItems"] != null) {
      selectEshopBoxItems = [];
      for (var element in (map["selectEshopBoxItems"] as List)) {
        selectEshopBoxItems.add(FilterBoxItem.fromJson(element));
      }
    }
  }

  Map<String, dynamic> toJson() {
    List platformBoxItem = [];
    for (FilterBoxItem platformBoxItems in platformBoxItems) {
      platformBoxItem.add(platformBoxItems.toJson());
    }
    List eshopBoxItem = [];
    for (FilterBoxItem eshopBoxItems in eshopBoxItems) {
      eshopBoxItem.add(eshopBoxItems.toJson());
    }
    List deliveryBoxItem = [];
    for (FilterBoxItem deliveryBoxItems in deliveryBoxItems) {
      deliveryBoxItem.add(deliveryBoxItems.toJson());
    }
    List selectPlatformBoxItem = [];
    for (FilterBoxItem selectPlatformBoxItems in selectPlatformBoxItems) {
      selectPlatformBoxItem.add(selectPlatformBoxItems.toJson());
    }
    List selectEshopBoxItem = [];
    for (FilterBoxItem selectEshopBoxItems in selectEshopBoxItems) {
      selectEshopBoxItem.add(selectEshopBoxItems.toJson());
    }

    return {
      "beforeTime": beforeTime,
      "afterTime": afterTime,
      "textStartTimeController":
          TextEditingController(text: textStartTimeController.text),
      "textEndTimeController":
          TextEditingController(text: textEndTimeController.text),
      "textBillNumberController":
          TextEditingController(text: textBillNumberController.text),
      "textPlatformTypeController":
          TextEditingController(text: textPlatformTypeController.text),
      "textEshopTypeController":
          TextEditingController(text: textEshopTypeController.text),
      "textDeliveryTypeController":
          TextEditingController(text: textDeliveryTypeController.text),
      "platformBoxItems": platformBoxItem,
      "eshopBoxItems": eshopBoxItem,
      "deliveryBoxItems": deliveryBoxItem,
      "selectPlatformBoxItems": selectPlatformBoxItem,
      "selectEshopBoxItems": selectEshopBoxItem,
    };
  }
}
